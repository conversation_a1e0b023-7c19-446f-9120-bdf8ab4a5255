<template>
    <div class="container_2208310930">
        <audio id="id_audio_2310081000" preload="auto" type="audio/wav"></audio>
        <div id="id_header_banner" class="header">
            <img src="@/assets/imgs/NW/NWZA12/img01.png" alt="">
        </div>
        <div class="box">
            <div class="box-content">
                <UUInputBox :orderInfo="orderInfo" @input="onTextInput" @editing="onTextEditing"></UUInputBox>
                <div class="premium">
                    <span class="number">{{ premiumObj.count }}</span>元{{ premiumObj.suffix }}/月<span class="light-txt"
                        v-if="premiumObj.suffix">（依据费率表）</span>
                </div>
                <div id="id_action_button" class="actionButton" @click="onSubmitClick">
                    立即投保
                    <img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
                </div>
                <div class="policy-box">
                    <van-icon class="policy-icon" :name="orderInfo.checked ? 'checked' : 'circle'"
                        @click="orderInfo.checked = !orderInfo.checked" />
                    我已阅读并同意
                    <span class="read" @click.stop="onViewPolicy('健康告知', true)">《健康告知》</span>
                    <span class="read" @click.stop="onViewPolicy('免责条款', true)">《免责条款》</span>
                    <span class="read" @click.stop="onViewPolicy('投保须知', true)">《投保须知》</span>
                    <span class="read" @click.stop="onViewPolicy('保险条款', true)">《保险条款》</span>
                    <span class="read" @click.stop="onViewPolicy('重要提示', true)">《重要提示》</span>
                    <span class="read" @click.stop="onViewPolicy('特别约定', true)">《特别约定》</span>
                    <span class="read" @click.stop="onViewPolicy('个人信息保护政策', true)">《个人信息保护政策》</span>
                    <span class="read" @click.stop="onViewPolicy('客户告知书', true)">《客户告知书》</span>
                    <span class="read" @click.stop="onViewPolicy('授权委托书与代扣服务协议', true)">《授权委托书与代扣服务协议》</span>
                    <br>
                    <span>保费与被保人年龄、医保情况相关，详情请见
                        <span class="read" @click.stop="onViewPolicy('费率表',)">《费率表》</span>
                    </span>
                </div>
            </div>
        </div>
        <HHPlan :obj="policyObj" :orderInfo="orderInfo" @click="({ name, isPolicy }) => onViewPolicy(name, isPolicy)">
        </HHPlan>
        <van-tabs v-model="currentTab" scrollspy sticky>
            <van-tab key="产品亮点" name="产品亮点" title="产品亮点">
                <div class="section">
                    <div class="section-title">产品亮点</div>
                    <img src="@/assets/imgs/NW/NWZA10/img02.png" alt="产品亮点">
                </div>
            </van-tab>
            <van-tab key="理赔说明" name="理赔说明" title="理赔说明">
                <div class="section">
                    <div class="section-title">理赔说明</div>
                    <HHClaimProcess></HHClaimProcess>
                </div>
            </van-tab>
            <van-tab key="常见问题" name="常见问题" title="常见问题">
                <div class="section">
                    <div class="section-title">常见问题</div>
                    <UUQuestionList :obj="policyObj"></UUQuestionList>
                </div>
            </van-tab>
        </van-tabs>
        <div class="copyright">
            <img src="@/assets/imgs/NW/NWZA08/footer.png" alt="底图">
            <p>该保险产品由众安在线财产保险股份有限公司承保并负责理赔，产品页面仅供参考，具体责任描述以保险合同为准。</p>
            <p>众安保险最近季度偿付能力符合监管要求，详情请参见众安保险官网(www.zhongan.com) 偿付能力信息披露。</p>
            <p>本产品由爱邦保险经纪有限公司销售，爱邦保险经纪有限公司版权所有</p>
            <p @click="onClickNwFilingNumber">粤ICP备2021097323号-1</p>
            <p>上海暖哇科技有限公司提供技术支持</p>
        </div>
        <div v-if="bottomButtonVisible" class="bottomButton" @click="onSubmitClick">
            立即投保<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <UUPolicyPopup :obj="policyObj" :orderInfo="orderInfo"></UUPolicyPopup>
        <UUFailPopup :obj="failObj" @click="jumpToFailLink"></UUFailPopup>
        <UUSilentPopup :obj="policyObj" :obj1="orderInfo" @click="silentUpgrade"></UUSilentPopup>
        <UUTabPopup :obj="policyObj" @ok="onAcceptPolicy" :orderInfo="orderInfo"></UUTabPopup>
        <HHWarnHint :obj="orderInfo" @policy="onViewPolicy('客户告知书', false)"></HHWarnHint>
        <NwRecord @SetIseeBiz="SetIseeBiz"></NwRecord>
    </div>
</template>

<script>
import moment from "moment";
import { isInWx, isMaskedAndT1Phone, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode, url_safe_b64_decode, } from "@/assets/js/common";
import { checkOrderParams, createOrderInfo, eventTracking, inputEndEditing, loadOrderInfo, saveOrderInfo, showToast, submitOrderInfo, } from "./function";
import HHPlan from "./components/HHPlan";
import HHClaimProcess from "./components/HHClaimProcess";
import UUFailPopup from "./components/UUFailPopup";
import UUTabPopup from "./components/UUTabPopup";
import UUInputBox from "./components/UUInputBox";
import UUSilentPopup from "./components/UUSilentPopup";
import UUQuestionList from "./components/UUQuestionList";
import UUPolicyPopup from "./components/UUPolicyPopup";
import HHWarnHint from "./components/HHWarnHint";
import NwRecord from "@/views/components/NwRecord";
import { fetchInfoByOrderNo, fetchRoundRobinWithAgeCommonFail, fetchStarPhoneV4, } from "@/api/insurance-api";
import { domainPathMap, audioObj2 } from "@/views/ZYBX/src";
import UUReservePopup from './components/UUReservePopup1';
import UUReserveSuccessPopup from './components/UUReserveSuccessPopup';
import { bxStorage } from '@/utils/store_util';
import { onClickNwFilingNumber } from '@/utils/filing_number';

export default {
    name: "Index9",
    components: {
        HHPlan, HHClaimProcess, HHWarnHint,
        UUPolicyPopup, UUQuestionList,
        UUInputBox, UUTabPopup,
        NwRecord, UUSilentPopup, UUFailPopup,
        UUReservePopup, UUReserveSuccessPopup,
    },
    data() {
        const orderInfo = createOrderInfo('direct');
        return {
            orderInfo,
            currentTab: '', // 切换的tab
            autoSubmit: true, // 自动拉起支付
            bottomButtonVisible: false,
            policyObj: { v: false, page: '', v1: false, page1: '', v2: false, isAccept: false, belongs: 'v1' },
            failObj: { visible: false, showTimer: false, path: '' },
            reserveTimer: null,
            hasShownReverse: false,
            reserveObj: { visible: false, traceType: reserveTraceTypeEnum.NO_INPUT },
            reserveSuccessObj: { visible: false },
            audioObj: audioObj2,
        }
    },
    computed: {
        orderPage() {
            const { source, action, school, identifier } = this.orderInfo;
            const productKey = school == 1 ? TraceLogInfoKeys.nw_za_bw_health_hm_jd_default_cube_base : TraceLogInfoKeys.nw_za_bw_health_hm_jd_cube_base;
            return `infoKey:${productKey}&page:${identifier}&act:${action}&src:${source}`;
        },
        // 投保费率数据
        premiumObj() {
            const { totalPremium } = this.orderInfo;
            if (totalPremium) {
                return { count: totalPremium, suffix: '' };
            }
            return { count: '0.6', suffix: '起' };
        },
    },
    created() {
        this.init();
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        const enterUpgrade = loadOrderInfo('NWZA12_Upgrade') || 0;
        if (enterUpgrade) {
            this.fetchInfoByOrderNo('', true);
        }

        this.$nextTick(() => { // 监听滚动事件
            this.actionButtonObserver();
            this.addObserverForBanner();
            this.timeOnPage();
        });

        const reserveTime = bxStorage.getObjItem('NWZA12_Reserve_Time')
        // 如果没预约过，这个值应该是空的，可以显示预约弹窗。
        // 如果已经预约过，这个值是请求接口的时间戳。
        // 不是当天，可以显示预约弹窗；当天只能预约一次，显示一次后不管什么情况，都不再显示。
        if (!reserveTime || !moment(reserveTime).isSame(moment(new Date()), 'day')) {
            this.reserveObj.traceType = reserveTraceTypeEnum.NO_INPUT;
            this.startReverseTimer(NO_INPUT_MAX_SECONDS);
        }

        this.makeAudioWork();

        if (this.orderInfo.action == 'forward') {
            this.refreshAudioSrc('jubao');
        } else {
            this.refreshAudioSrc('toubao');
        }
    },
    beforeDestroy() {
        this.closeReverseTimer();
    },
    methods: {
        makeAudioWork() {
            const func = () => {
                try {
                    const oAudio = document.querySelector('#id_audio_2310081000');
                    if (oAudio && !oAudio.alreadyPlay) {
                        oAudio.play().then(() => {
                            oAudio.alreadyPlay = true;
                        });

                        oAudio.addEventListener("canplay", () => {
                            console.log('音频可播放', Date.now());
                            oAudio.play(); // 音频可流畅播放时触发
                        });

                        // 监听播放结束事件
                        oAudio.addEventListener('ended', () => {
                            console.log('音频播放结束', Date.now());
                            for (let key in this.audioObj) {
                                const arr = this.audioObj[key];
                                const idx = arr.indexOf(oAudio.src);
                                if (idx >= 0) {
                                    setTimeout(() => {
                                        const tmp = arr[idx + 1];
                                        if (tmp) {
                                            oAudio.src = tmp;
                                            oAudio.load();
                                        }
                                    }, 3000);

                                    return;
                                }
                            }
                        });
                    }
                } catch (e) {

                }
            };

            window.onclick = func;
            window.ontouchstart = func;
        },

        refreshAudioSrc(key) {
            const oAudio = document.querySelector('#id_audio_2310081000');
            if (!oAudio) {
                return;
            }
            if (oAudio.src.indexOf(key) >= 0) {
                return;
            }
            const arr = this.audioObj[key];
            if (!arr) {
                return '';
            }
            oAudio.src = arr[0];
            oAudio.load();
        },

        onClickNwFilingNumber,
        SetIseeBiz(value) {
            this.orderInfo.traceBackUuid = value;
        },

        timeOnPage() {
            const channel = this.orderInfo.channel;
            const action = (function () {
                let idx = 0;
                const start = Date.now();
                return function () {
                    try {
                        if (idx <= 0) {
                            idx++;
                            const time = Date.now() - start;
                            const params = { "page": `暖哇众安百万2024惠民版NWZA12Index9(nw_za_bw_health_hm_jd_default_cube_base)-停留时间(${time})`, "channel": channel, "infoKey": "nw_za_bw_health_hm_jd_default_cube_base", "time": time };
                            const blob = new Blob([JSON.stringify(params)], { type: 'application/json;charset=UTF-8' });
                            navigator.sendBeacon('https://sr0llef8bo_5zb19brqip8t-3rz_7c0.aibangbaoxian.net/marketprod/Insurance/trace/v2/log', blob);
                        }
                    } catch (e) {

                    }
                }
            })();

            // 使用beforeunload事件
            window.addEventListener('beforeunload', (event) => {
                action();
            });

            // 使用visibilitychange事件
            document.addEventListener('visibilitychange', () => {
                if (document.visibilityState === 'hidden') {
                    action();
                }
            });
        },
        actionButtonObserver() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries, observer) => {
                    this.bottomButtonVisible = !entries[0].isIntersecting;
                }, { threshold: 0.10 });

                const buttonNode = document.getElementById('id_action_button');
                buttonNode && observer.observe(buttonNode);
            }
        },
        addObserverForBanner() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries, observer) => {
                    const entry = entries[0];
                    if (!entry.isIntersecting) {
                        observer.unobserve(entry.target);
                        this.scrollTimeReport();
                    }
                }, { threshold: 0.10 });

                const buttonNode = document.getElementById('id_header_banner');
                buttonNode && observer.observe(buttonNode);
            }
        },

        onAcceptPolicy() {
            this.reserveObj.traceType = reserveTraceTypeEnum.NO_ORDER;
            this.startReverseTimer(NO_ORDER_MAX_SECONDS);
            this.orderInfo.checked = true;
            if (this.orderInfo.v2) {
                return;
            }
            if (this.policyObj.isAccept) {
                this.refreshAudioSrc('xiadan');
                return this.policyObj.v2 = true;
            }
            this.onSubmitClick();
        },

        silentUpgrade(value) {
            this._actionTracking(`首页：${value ? '默认升级' : '正常升级'}`);
            this.orderInfo.school = value ? 1 : 0;

            this.onSubmitOrder();
        },
        // 初始化
        init() {
            const query = this.$route.query || {};
            query.source = query.source || 'direct'; // source要以链接携带的参数为准
            query.action = query.action || 'direct'; // action要以链接携带的参数为准
            query.sourcePage = query.sourcePage || ''; // sourcePage要以链接携带的参数为准
            query.channelCode = query.cld || query.channelCode || '';

            const inStore = loadOrderInfo() || {};
            Object.assign(this.orderInfo, inStore, query);

            try {
                if (query.bizParams) {
                    const params = JSON.parse(url_safe_b64_decode(query.bizParams));
                    Object.assign(this.orderInfo, params);
                }
            } catch (error) {
                this._actionTracking(`bizParams解析失败`);
            }

            this.orderInfo.identifier = 'NWZA12Index9';
            this.orderInfo.productKey = TraceLogInfoKeys.nw_za_bw_health_hm_jd_default_cube_base;
            this.orderInfo.school = '1';
            this.orderInfo.checked = this.orderInfo.channel >= 1000;
            this.orderInfo.isLowPricePage = true;

            const { phoneNo, starPhone, mTel } = this.orderInfo;
            if (!isPhoneNum(phoneNo) && isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.phoneNo = starPhone;
            } else {
                this.orderInfo.starPhone = '';
            }

            this.fetchPhoneNumber();
        },
        // 手机号解密
        fetchPhoneNumber() {
            const { m, phoneNo, starPhone, mTel } = this.orderInfo;
            if (!m || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
                return this._entryReport();
            }

            const params = { encryptContent: m };
            fetchStarPhoneV4(params).then(res => {
                const { encryptPhone, showPhone } = res.data;
                if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
                    this.orderInfo.mTel = encryptPhone;
                    this.orderInfo.starPhone = showPhone;
                    saveOrderInfo(this.orderInfo);
                }
            }).finally(() => {
                return this._entryReport();
            });
        },
        // 输入框输入，自动拉起下单
        onTextInput() {
            saveOrderInfo(this.orderInfo);

            if (this.autoSubmit) {
                this.onSubmitClick('AUTO');
            }
        },
        // 输入框任意字符变化（input事件）
        onTextEditing() {
            if (this.hasShownReverse) {
                return;
            }
            this.reserveObj.traceType = reserveTraceTypeEnum.NO_INPUT;
            this.startReverseTimer(NO_INPUT_MAX_SECONDS);
        },
        // 点击提交按钮
        onSubmitClick(from) {
            // 只输入完身份证号或者手机号，也会触发到这里，
            // 只有当信息全部输入完毕，点击提交按钮后，显示条款或者升级弹窗，才能将线索类型从待投保改为待升级
            if (from === 'AUTO') {
                this.reserveObj.traceType = reserveTraceTypeEnum.NO_INPUT;
                if (!this.hasShownReverse) {
                    this.startReverseTimer(NO_INPUT_MAX_SECONDS);
                }
            } else {
                this.reserveObj.traceType = reserveTraceTypeEnum.NO_ORDER;
                if (!this.hasShownReverse) {
                    this.startReverseTimer(NO_ORDER_MAX_SECONDS);
                }
            }

            const { code, msg, } = checkOrderParams(this.orderInfo, this.orderPage);
            if (from != 'AUTO' || code == 0 || msg == '用户协议未同意') {
                this._actionTracking('点击立即投保按钮');
            }
            if (code != 0) {
                if (msg == '用户协议未同意') {
                    this.showToastAndEndEdit();

                    return this.onViewPolicy('健康告知', true, true);
                }
                return (from != 'AUTO') && this.showToastAndEndEdit(msg);
            }

            this.showToastAndEndEdit();
            this.policyObj.v2 = true;
            this.refreshAudioSrc('xiadan');
        },

        onSubmitOrder() {
            this.showToastAndEndEdit();

            this.$toast.loading({
                message: '订单提交中\n请稍候',
                forbidClick: true,
                duration: 0,
            });

            this.createSubmitOrder();
        },

        createSubmitOrder() {
            this._actionTracking('点击立即领取按钮');

            const { code, msg, params } = checkOrderParams(this.orderInfo, this.orderPage);
            if (this.orderInfo.school != 1) {
                params.planKey = TraceLogInfoKeys.nw_za_bw_health_hm_jd_cube_base;
            }

            params.extendParams = JSON.stringify(params.extendParams || {});

            submitOrderInfo(params, this.orderInfo,).then(url => {
                this.$toast.clear(true);
                window.location.href = url;
            }).catch(err => {
                this.$toast.clear(true);
                const message = err.msg || '';
                if (message.includes('未通过') || message.includes('为家人投保')) {
                    return this.showToastAndEndEdit(message);
                }
                if (!message.includes('不能重复购买') && !message.includes('已存在保单') && !message.includes('投保份数')) {
                    return this.fetchFailPath();
                }
                this.fetchInfoByOrderNo(message);
            }).finally(() => {
                saveOrderInfo(this.orderInfo);
            });
        },

        // 根据orderNo查询订单信息
        fetchInfoByOrderNo(message, enterUpgrade) {
            const { relation, callbackUrl } = this.orderInfo;
            const idCardNo = this.orderInfo[`idCard${relation}`];
            const params = { infoKey: TraceLogInfoKeys.nw_za_bw_health_hm_jd_cube_base, insuredIdCard: idCardNo };

            fetchInfoByOrderNo(params).then(r => {
                const { code, data } = r.data;
                if (code != 2000 || !data) {
                    return !enterUpgrade && this.fetchFailPath();
                }

                const { infoNo } = data || {};
                if (infoNo && callbackUrl && callbackUrl.indexOf('http') >= 0) {
                    const params = `infoNo=${infoNo}`;
                    let href = callbackUrl.replace(/&?infoNo=[^?&]*/ig, '');
                    href = href.indexOf('?') > 0 ? `${href}&${params}` : `${href}?${params}`;

                    return window.location.href = href;
                }

                return !enterUpgrade && this.fetchFailPath();
            }).catch((err) => {
                return !enterUpgrade && this.fetchFailPath();
            });
        },

        fetchFailPath() {
            const { channel, phoneNo, mTel, relation } = this.orderInfo;

            const params = {
                channelId: channel,
                idCard: this.orderInfo[`idCard${relation}`],
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'nw_mf_common_fail',
                currentPage: 'NWZA12',
            };

            fetchRoundRobinWithAgeCommonFail(params).then(result => {
                const { path } = result.data || {};
                this.failObj.path = path;
            }).finally(() => {
                if (this.failObj.path == 'NotFound') {
                    return this.showToastAndEndEdit('投保失败，您可以选择为家人投保');
                }

                if (!this.failObj.path) {
                    this.failObj.path = 'NWDD01Index1';
                }

                this.failObj = { ...this.failObj, visible: true, showTimer: true, };
                this._actionTracking(`显示核保失败弹窗(${this.failObj.path})`);
            });
        },

        jumpToFailLink() {
            let path = this.failObj.path;
            if (!domainPathMap[path]) {
                path = 'NWDD01Index1';
            }
            this._actionTracking(`点击核保失败图片(${path})`);

            const { channel, relation, mTel, phoneNo, channelCode, starPhone, identifier, name1, idCard1 } = this.orderInfo;

            const params = {
                channel, cld: channelCode, mTel, relation, source: identifier, action: 'forward',
                name1, idCard1, [`name${relation}`]: this.orderInfo[`name${relation}`], [`idCard${relation}`]: this.orderInfo[`idCard${relation}`]
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else if (isMaskedAndT1Phone(starPhone, mTel)) {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));

            setTimeout(() => {
                const href = domainPathMap[path];
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
        },

        onViewPolicy(name, isPolicy, isAccept) {
            this.policyObj.isAccept = isAccept;
            this.policyObj.belongs = 'v1';
            this.policyObj[isPolicy ? 'v1' : 'v'] = true;
            this.policyObj[isPolicy ? 'page1' : 'page'] = name;
        },

        closeReverseTimer() {
            clearTimeout(this.reserveTimer);
            this.reserveTimer = null;
        },
        handleReserveTimer() {
            this.$data.reserveObj.visible = true;
            this.hasShownReverse = true;
            this.closeReverseTimer();
        },
        startReverseTimer(second = NO_INPUT_MAX_SECONDS) {
            this.closeReverseTimer();
            if (this.orderInfo.phoneNo.length !== 11 && this.orderInfo.mTel !== 11) {
                // 没有完整输入手机号，不需要计时
                return;
            }
            if (this.hasShownReverse) {
                // 今天已经提示预约过，不需要计时，不需要再提示预约
                return;
            }
            this.reserveTimer = setTimeout(this.handleReserveTimer, second * 1000);
        },
        // 预约弹窗关闭
        onReverseClose(showSuccess = false) {
            this.closeReverseTimer();
            this.reserveObj.visible = false;
            if (showSuccess) {
                this.reserveSuccessObj.visible = true;
            }
        },
        // 预约成功弹窗关闭
        onReverseSuccessClose() {
            this.reserveObj.visible = false;
            this.reserveSuccessObj.visible = false;
        },

        scrollTimeReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd } = timing || {};
            const detentionTime = moment() - domContentLoadedEventEnd;
            this._actionTracking('首页滚动', detentionTime);
        },
        _entryReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);
        },
        _actionTracking(name, time = 0) {
            eventTracking(this.orderInfo, name, time);
        },
        showToastAndEndEdit(message) {
            message && showToast(message);
            inputEndEditing();
            document.body.scrollTop = document.documentElement.scrollTop = 300;
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2208310930 {
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.15rem;
    background-color: #f8f8f8;

    img {
        display: block;
        max-width: 100%;
    }

    .section {
        background-color: #ffffff;

        .section-title {
            display: flex;
            justify-content: center;
            align-items: center;

            color: #333333;
            font-size: 0.18rem;
            font-weight: 500;
            line-height: 0.45rem;

            &::before,
            &::after {
                content: " ";
                width: 0.55rem;
                height: 0.13rem;
                background: no-repeat center/100%;
            }

            &::before {
                margin-right: 0.1rem;
                background-image: url("~@/assets/imgs/common/icon_needle_left.png");
            }

            &::after {
                margin-left: 0.1rem;
                background-image: url("~@/assets/imgs/common/icon_needle_right.png");
            }
        }
    }

    .box {
        position: relative;
        margin: -1.2rem 0.1rem 0.15rem;
        overflow: hidden;
        border-radius: 0.1rem;
        background-color: #ffffff;

        .box-content {
            background-color: #ffffff;

            .premium {
                margin: 0.15rem 0 0.1rem;
                font-size: 0.16rem;
                font-weight: 500;
                text-align: center;

                .number {
                    margin: 0 0.05rem;
                    font-size: 0.2rem;
                    color: #ff4509;
                }

                .light-txt {
                    font-size: 0.12rem;
                    color: #b3b3b3;
                }
            }

            .actionButton {
                position: relative;
                margin: 0.15rem auto;
                padding: 0.15rem 0;
                width: 3rem;
                border-radius: 999px;
                box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
                background: linear-gradient(270deg,
                        rgb(255, 16, 46),
                        rgb(253, 123, 69));
                animation: button_animate 1.35s linear infinite;

                font-size: 0.2rem;
                color: #ffffff;
                font-weight: 700;
                text-align: center;

                .hand {
                    position: absolute;
                    top: 0.25rem;
                    left: 75%;
                    width: 18%;
                    animation: hand_animate 1s linear infinite;
                }
            }

            .policy-box {
                padding: 0.08rem;
                border-radius: 0.06rem;
                background-color: #ffffff;

                font-size: 0.12rem;
                color: #666666;
                line-height: 0.2rem;
                text-align: justify;

                .policy-icon {
                    color: #ff4509;
                    font-size: 0.16rem;
                    vertical-align: -0.01rem;
                }

                .read {
                    color: #ff4509;
                    font-weight: 500;
                }
            }
        }
    }

    /deep/ .van-tabs--line .van-tabs__wrap {
        margin-bottom: 0.1rem;
    }

    .bottomButton {
        position: fixed;
        inset: auto 0 0.25rem 0;
        margin: 0 auto;
        padding: 0.15rem 0;
        width: 3rem;
        font-size: 0.2rem;
        color: #ffffff;
        font-weight: 700;
        text-align: center;
        border-radius: 999px;
        box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
        background: linear-gradient(270deg,
                rgb(255, 16, 46),
                rgb(253, 123, 69));
        animation: button_animate 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.25rem;
            left: 75%;
            width: 18%;
            animation: hand_animate 1s linear infinite;
        }
    }

    .copyright {
        padding: 0.2rem 0.15rem 0.85rem;
        color: #666666;
        font-size: 0.12rem;
        line-height: 1.5;
        text-align: center;

        p {
            padding: 0.04rem 0;
        }
    }

    @keyframes button_animate {
        0% {
            transform: scale(1);
        }

        40% {
            transform: scale(1);
        }

        70% {
            transform: scale(0.95);
        }

        100% {
            transform: scale(1);
        }
    }

    @keyframes hand_animate {
        0% {
            transform: translate(-0.1rem, -0.1rem);
        }

        45% {
            transform: translate(0.1rem, 0);
        }

        70% {
            transform: translate(0.1rem, 0);
        }

        100% {
            transform: translate(-0.1rem, -0.1rem);
        }
    }
}
</style>
