# 语音交互功能说明

## 功能概述

本页面实现了智能语音交互功能，包括：

1. **用户无操作检测**：当用户在页面10秒内没有进行任何操作时，自动播放引导语音并弹出授权弹窗
2. **语音授权弹窗**：用户可以选择同意或拒绝语音服务授权
3. **双向语音交互**：用户同意授权后，启用H5通话功能，实现双向语音交互
4. **单向全流程引导**：用户拒绝授权时，提供单向语音引导帮助用户完成表单填写
5. **操作节点埋点**：记录用户在各个操作节点的行为，用于分析语音交互触发点

## 主要组件

### 1. HHVoiceAuthPopup.vue
语音授权弹窗组件，包含：
- 授权说明界面
- 同意/拒绝按钮
- 隐私保护提示

### 2. Index26.vue 新增功能
- `initUserActivityDetection()`: 初始化用户活动检测
- `handleUserInactivity()`: 处理用户无操作情况
- `onVoiceAuthAccept()`: 处理用户同意授权
- `onVoiceAuthReject()`: 处理用户拒绝授权
- `startUnidirectionalGuide()`: 启动单向引导
- `recordVoiceInteractionNode()`: 记录语音交互节点

## 操作节点埋点

系统会在以下节点记录用户行为：
- 进入页面
- 聚焦姓名输入框
- 输入姓名
- 失焦姓名输入框
- 聚焦身份证号输入框
- 输入身份证号
- 失焦身份证号输入框
- 聚焦手机号输入框
- 输入手机号
- 失焦手机号输入框
- 用户同意/拒绝语音授权
- 双向语音交互启动

## 使用流程

1. **页面加载**：用户进入页面，开始10秒无操作检测
2. **无操作触发**：10秒内无操作时，播放引导语音并显示授权弹窗
3. **用户选择**：
   - **同意授权**：启用H5通话功能，开始双向语音交互
   - **拒绝授权**：启动单向全流程引导，该逻辑只执行一次
4. **持续服务**：根据用户选择提供相应的语音服务

## 技术实现

### 用户活动检测
```javascript
// 监听的用户活动事件
const events = ['click', 'touchstart', 'scroll', 'keydown', 'input'];
```

### 埋点数据结构
```javascript
{
    node: '操作节点名称',
    timestamp: Date.now(),
    step: '当前步骤'
}
```

### H5通话集成
- 通过URL参数 `h5CallEnabled=1` 启用H5通话
- 自动设置埋点追踪数据
- 处理通话初始化成功/失败事件

## 注意事项

1. **浏览器兼容性**：语音功能需要现代浏览器支持
2. **用户权限**：需要用户授权才能使用麦克风
3. **网络要求**：双向语音交互需要稳定的网络连接
4. **隐私保护**：所有语音数据都经过加密处理

## 待完善功能

1. **引导语音文件**：等待白泽提供具体的引导语音文件
2. **TTS集成**：可以集成文本转语音功能
3. **语音识别**：可以添加语音转文本功能
4. **智能对话**：可以集成AI对话功能

## 测试建议

1. 测试10秒无操作检测是否正常触发
2. 测试授权弹窗的显示和交互
3. 测试同意授权后H5通话是否正常启动
4. 测试拒绝授权后单向引导是否正常工作
5. 测试各个操作节点的埋点是否正确记录
