import Vue from 'vue'
import App from './App.vue'
// import router from "./router"
import SvgIcon from '@/components/SvgIcon'
import router from "./router/index"
import VueClipboard from 'vue-clipboard2'
import 'mint-ui/lib/style.css'
import VueWechatTitle from 'vue-wechat-title'
import { Button, Toast, Form, Field, Popup, Icon, Tabs, Tab, Step, Steps, RadioGroup, Radio, Calendar } from 'vant';
import 'vant/es/toast/style';
import 'vant/es/dialog/style';
import $ from 'jquery'
import {exceptionReport} from "@/api/insurance-api";
import VueLazyload from 'vue-lazyload'

Vue.prototype.version = process.env.VUE_APP_VERSION
/*全局cdn地址前缀*/
Vue.prototype.cdnLink = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/picture'
//通过打包命令区分保泰跟I云保方cdn地址
Vue.prototype.btCdnLink =['kdprod'].includes(process.env.VUE_APP_TITLE) ? 'https://static.iyb.tm/web/h5/channel/pctx/picture' : 'https://cdns.bountech.com/marketfront/file/insurance/cdn/picture'
Vue.prototype.isRrb = window.location.host.includes('rrbxw')
Vue.prototype.isHnkd = !Vue.prototype.isRrb && window.location.host.includes('bjhnkd')
Vue.prototype.isKaiSen = !Vue.prototype.isRrb && !Vue.prototype.isHnkd && (window.location.host.includes('kaisen') || window.location.host.includes('bsiin') || window.location.host.includes('sknii') || window.location.host.includes('ksini'))
Vue.prototype.isNW = !Vue.prototype.isRrb && !Vue.prototype.isHnkd && !Vue.prototype.isKaiSen && window.location.host.includes('aibangbaoxian');
Vue.prototype.isZhelibao = !Vue.prototype.isRrb && !Vue.prototype.isHnkd && !Vue.prototype.isKaiSen && !Vue.prototype.isNW && window.location.host.includes('zhelibao')
Vue.prototype.isZhongJia = !Vue.prototype.isRrb && !Vue.prototype.isHnkd && !Vue.prototype.isKaiSen && !Vue.prototype.isNW && !Vue.prototype.isZhelibao && window.location.host.includes('zjic.com') || window.location.host.includes('192.168') || window.location.host.includes('uat')
Vue.prototype.isNW1 = !Vue.prototype.isRrb && !Vue.prototype.isHnkd && !Vue.prototype.isKaiSen && !Vue.prototype.isNW && !Vue.prototype.isZhelibao && !Vue.prototype.isZhongJia && window.location.host.includes('anbxyx')

Vue.prototype.insCompany = { name: '保通保险代理有限公司' , abbr:'保通', number: '粤ICP备17147734号-1', extra:'本产品的销售咨询服务由保通保险代理有限公司提供'}
if (Vue.prototype.isNW) {
    Vue.prototype.insCompany = { name: '爱邦保险经纪有限公司', abbr:'爱邦', number: '粤ICP备2021097323号-1'}
} else if (Vue.prototype.isNW1) {
    Vue.prototype.insCompany = { name: '爱邦保险经纪有限公司', abbr:'爱邦', number: '粤ICP备2021169710号-11'}
} else if (Vue.prototype.isKaiSen) {
    Vue.prototype.insCompany = { name: '陕西凯森保险代理有限公司', abbr:'凯森', number: '陕ICP备13000203号-7'}
} else if (Vue.prototype.isHnkd) {
    Vue.prototype.insCompany = { name: '北京惠诺康达保险代理有限公司' , abbr:'惠诺康达', number: '京ICP备19017143号-1', isHidden: true }
} else if (Vue.prototype.isRrb) {
    Vue.prototype.insCompany = { name: '广东人人保险代理有限公司' , abbr:'广东人人', number: '粤ICP备2024169957号', isHidden: true, extra:'本页面由上海豹云网络信息服务有限公司提供技术服务'}
} else if (Vue.prototype.isZhongJia) {
    Vue.prototype.insCompany = { name: '中佳保险代理有限公司' , abbr:'中佳', number: '京ICP备12001493号-1' }
}

Vue.config.productionTip = false
Vue.use(VueWechatTitle)
Vue.component('svg-icon', SvgIcon)

const errorObj = {};
// window.jQuery = $;
window.$ = $;
Vue.config.errorHandler = function (error, node, info) {
    const { message, name, stack } = error;

    let errorPage = '';
    if (node && node.orderInfo) {
        const {identifier, page} = node.orderInfo;
        errorPage = identifier || page || '';
    }

    const errorStack = (stack || '').slice(0,990);
    console.log(`页面异常 =>`, errorStack);
    if (errorStack.indexOf('PDFJSWrapper') >= 0) {
        return;
    }
    const errorMessage = (message || '').slice(0,990);
    const errorType = name || '';
    const errorInfo = info || '';
    const key = `${errorPage}#${errorType}#${errorInfo}`;
    if (errorObj[key]) {
        return;
    }

    errorObj[key] = 1;

    const params = {
        "errorType":errorType,
        "errorInfo":errorInfo,
        "errorMessage":errorMessage,
        "errorStack":errorStack,
        "page":errorPage,
    };

    exceptionReport(params).then(res => {

    }).catch(err => {

    });
};

// router.beforeEach((to, from, next) => {
//     var state = {
//         title: "title",
//         url: "#"
//     };
//
//     window.history.pushState(state, "title", "#");
//
//     next();
// })

router.beforeEach((to, from, next) => {
    /* 路由发生变化修改页面title */
    if (to.meta.title) {
        document.title = to.meta.title
    }
    next()
})


new Vue({
    router,
    render: h => h(App),
}).$mount('#app')
Vue.use(Button).use(Toast).use(Form).use(Field).use(Popup).use(Icon).use(Tabs).use(Tab).use(VueClipboard).use(Steps).use(Step).use(RadioGroup).use(Radio).use(Calendar)
    .use(VueLazyload)
