<template>
    <van-popup v-model="obj.visible" class="voice-auth-popup" position="center" :close-on-click-overlay="false">
        <div class="popup-content">
            <div class="popup-header">
                <div class="title">语音服务授权</div>
                <div class="subtitle">为了给您提供更好的服务体验</div>
            </div>
            
            <div class="popup-body">
                <div class="icon-container">
                    <div class="voice-icon">🎤</div>
                </div>
                
                <div class="description">
                    <p>我们希望为您提供智能语音交互服务</p>
                    <p>需要您的授权才能开启双向语音通话</p>
                </div>
                
                <div class="features">
                    <div class="feature-item">
                        <span class="feature-icon">🎯</span>
                        <span class="feature-text">智能语音引导</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">💬</span>
                        <span class="feature-text">双向语音交互</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🔒</span>
                        <span class="feature-text">信息安全保护</span>
                    </div>
                </div>
            </div>
            
            <div class="popup-footer">
                <div class="button-group">
                    <button class="btn btn-reject" @click="onReject">
                        暂不需要
                    </button>
                    <button class="btn btn-accept" @click="onAccept">
                        立即授权
                    </button>
                </div>
                
                <div class="privacy-notice">
                    点击"立即授权"即表示您同意开启语音服务
                </div>
            </div>
        </div>
    </van-popup>
</template>

<script>
export default {
    name: "HHVoiceAuthPopup",
    props: {
        obj: {
            type: Object,
            default: () => ({
                visible: false
            })
        }
    },
    methods: {
        onAccept() {
            this.obj.visible = false;
            this.$emit('accept');
        },
        
        onReject() {
            this.obj.visible = false;
            this.$emit('reject');
        }
    }
}
</script>

<style lang="less" scoped>
.voice-auth-popup {
    background-color: transparent;
    
    .popup-content {
        width: 3.2rem;
        background: #ffffff;
        border-radius: 0.15rem;
        overflow: hidden;
        box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.2);
    }
    
    .popup-header {
        padding: 0.2rem 0.15rem 0.1rem;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        
        .title {
            font-size: 0.18rem;
            font-weight: 600;
            margin-bottom: 0.05rem;
        }
        
        .subtitle {
            font-size: 0.13rem;
            opacity: 0.9;
        }
    }
    
    .popup-body {
        padding: 0.2rem 0.15rem;
        
        .icon-container {
            text-align: center;
            margin-bottom: 0.15rem;
            
            .voice-icon {
                width: 0.6rem;
                height: 0.6rem;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.3rem;
                color: white;
            }
        }
        
        .description {
            text-align: center;
            margin-bottom: 0.2rem;
            
            p {
                margin: 0.05rem 0;
                font-size: 0.14rem;
                color: #333;
                line-height: 1.4;
            }
        }
        
        .features {
            display: flex;
            justify-content: space-around;
            margin-bottom: 0.1rem;
            
            .feature-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;
                
                .feature-icon {
                    font-size: 0.2rem;
                    margin-bottom: 0.05rem;
                }
                
                .feature-text {
                    font-size: 0.12rem;
                    color: #666;
                    text-align: center;
                }
            }
        }
    }
    
    .popup-footer {
        padding: 0.15rem;
        
        .button-group {
            display: flex;
            gap: 0.1rem;
            margin-bottom: 0.1rem;
            
            .btn {
                flex: 1;
                padding: 0.12rem 0;
                border: none;
                border-radius: 0.06rem;
                font-size: 0.15rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &.btn-reject {
                    background: #f5f5f5;
                    color: #666;
                    
                    &:active {
                        background: #e8e8e8;
                    }
                }
                
                &.btn-accept {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    
                    &:active {
                        opacity: 0.8;
                    }
                }
            }
        }
        
        .privacy-notice {
            font-size: 0.11rem;
            color: #999;
            text-align: center;
            line-height: 1.3;
        }
    }
}
</style>
